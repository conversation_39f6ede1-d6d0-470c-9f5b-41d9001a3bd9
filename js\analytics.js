/**
 * EWIN ELECTRICAL - Analytics
 * Privacy-friendly analytics with console logging (placeholder for real analytics)
 */

const Analytics = {
    // Configuration
    config: {
        debug: true,
        trackPageViews: true,
        trackUserInteractions: true,
        trackFormSubmissions: true,
        trackScrollDepth: true,
        trackTimeOnPage: true
    },

    // State
    state: {
        sessionId: null,
        pageLoadTime: null,
        scrollDepthMarkers: [25, 50, 75, 90],
        maxScrollDepth: 0,
        timeOnPageInterval: null,
        timeOnPage: 0
    },

    // Initialize analytics
    init() {
        this.generateSessionId();
        this.trackPageLoad();
        this.bindEvents();
        this.startTimeTracking();
        
        if (this.config.debug) {
            console.log('Analytics initialized', {
                sessionId: this.state.sessionId,
                config: this.config
            });
        }
    },

    // Generate unique session ID
    generateSessionId() {
        this.state.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // Track page load
    trackPageLoad() {
        this.state.pageLoadTime = Date.now();
        
        const pageData = {
            page: window.location.pathname,
            title: document.title,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            timestamp: this.state.pageLoadTime,
            sessionId: this.state.sessionId
        };

        this.logEvent('page_view', pageData);

        // Track page performance
        if (window.performance && window.performance.timing) {
            window.addEventListener('load', () => {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                
                this.logEvent('page_performance', {
                    loadTime,
                    domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                    firstPaint: timing.responseStart - timing.navigationStart
                });
            });
        }
    },

    // Bind event listeners
    bindEvents() {
        // Scroll depth tracking
        if (this.config.trackScrollDepth) {
            window.addEventListener('scroll', this.throttle(this.trackScrollDepth.bind(this), 250));
        }

        // Click tracking
        if (this.config.trackUserInteractions) {
            document.addEventListener('click', this.trackClick.bind(this));
        }

        // Form tracking
        if (this.config.trackFormSubmissions) {
            document.addEventListener('submit', this.trackFormSubmission.bind(this));
        }

        // Page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        // Before page unload
        window.addEventListener('beforeunload', this.trackPageUnload.bind(this));
    },

    // Track scroll depth
    trackScrollDepth() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = Math.round((scrollTop / documentHeight) * 100);

        if (scrollPercent > this.state.maxScrollDepth) {
            this.state.maxScrollDepth = scrollPercent;

            // Check if we've hit any markers
            this.state.scrollDepthMarkers.forEach(marker => {
                if (scrollPercent >= marker && !this.state[`scrollDepth${marker}`]) {
                    this.state[`scrollDepth${marker}`] = true;
                    this.logEvent('scroll_depth', {
                        depth: marker,
                        page: window.location.pathname
                    });
                }
            });
        }
    },

    // Track clicks
    trackClick(e) {
        const element = e.target;
        const tagName = element.tagName.toLowerCase();
        
        // Track specific elements
        const trackableElements = ['a', 'button'];
        if (!trackableElements.includes(tagName)) return;

        const data = {
            element: tagName,
            text: element.textContent?.trim().substring(0, 100) || '',
            href: element.href || '',
            className: element.className || '',
            id: element.id || '',
            page: window.location.pathname
        };

        // Special tracking for specific elements
        if (element.matches('[data-calendly-popup]')) {
            data.action = 'calendly_open';
        } else if (element.matches('.btn--primary')) {
            data.action = 'primary_cta_click';
        } else if (element.matches('.btn--secondary')) {
            data.action = 'secondary_cta_click';
        } else if (element.matches('.header__nav-link')) {
            data.action = 'nav_click';
        } else if (element.matches('a[href^="tel:"]')) {
            data.action = 'phone_click';
        } else if (element.matches('a[href^="mailto:"]')) {
            data.action = 'email_click';
        }

        this.logEvent('click', data);
    },

    // Track form submissions
    trackFormSubmission(e) {
        const form = e.target;
        if (!form.matches('form')) return;

        const formData = new FormData(form);
        const data = {
            formId: form.id || 'unknown',
            formClass: form.className || '',
            fields: Array.from(formData.keys()),
            page: window.location.pathname
        };

        this.logEvent('form_submit', data);
    },

    // Handle visibility changes
    handleVisibilityChange() {
        if (document.hidden) {
            this.logEvent('page_hidden', {
                timeOnPage: this.state.timeOnPage,
                maxScrollDepth: this.state.maxScrollDepth
            });
            this.stopTimeTracking();
        } else {
            this.logEvent('page_visible');
            this.startTimeTracking();
        }
    },

    // Track page unload
    trackPageUnload() {
        this.logEvent('page_unload', {
            timeOnPage: this.state.timeOnPage,
            maxScrollDepth: this.state.maxScrollDepth,
            sessionDuration: Date.now() - this.state.pageLoadTime
        });
    },

    // Start time tracking
    startTimeTracking() {
        if (!this.config.trackTimeOnPage) return;

        this.stopTimeTracking(); // Clear any existing interval
        
        this.state.timeOnPageInterval = setInterval(() => {
            this.state.timeOnPage += 1;
            
            // Log milestone events
            if (this.state.timeOnPage % 30 === 0) { // Every 30 seconds
                this.logEvent('time_milestone', {
                    timeOnPage: this.state.timeOnPage,
                    page: window.location.pathname
                });
            }
        }, 1000);
    },

    // Stop time tracking
    stopTimeTracking() {
        if (this.state.timeOnPageInterval) {
            clearInterval(this.state.timeOnPageInterval);
            this.state.timeOnPageInterval = null;
        }
    },

    // Log event (main logging function)
    logEvent(eventName, data = {}) {
        const eventData = {
            event: eventName,
            timestamp: Date.now(),
            sessionId: this.state.sessionId,
            url: window.location.href,
            ...data
        };

        // Console logging for development
        if (this.config.debug) {
            console.log('📊 Analytics Event:', eventName, eventData);
        }

        // Here you would integrate with your analytics service
        // Examples:
        // - Google Analytics 4: gtag('event', eventName, data);
        // - Mixpanel: mixpanel.track(eventName, eventData);
        // - Custom API: fetch('/api/analytics', { method: 'POST', body: JSON.stringify(eventData) });
        
        // For now, we'll store in localStorage for demonstration
        this.storeEvent(eventData);
    },

    // Store event locally (for demonstration)
    storeEvent(eventData) {
        try {
            const events = JSON.parse(localStorage.getItem('ewin_analytics') || '[]');
            events.push(eventData);
            
            // Keep only last 100 events
            if (events.length > 100) {
                events.splice(0, events.length - 100);
            }
            
            localStorage.setItem('ewin_analytics', JSON.stringify(events));
        } catch (error) {
            console.warn('Failed to store analytics event:', error);
        }
    },

    // Get stored events (for debugging)
    getStoredEvents() {
        try {
            return JSON.parse(localStorage.getItem('ewin_analytics') || '[]');
        } catch (error) {
            console.warn('Failed to retrieve analytics events:', error);
            return [];
        }
    },

    // Clear stored events
    clearStoredEvents() {
        localStorage.removeItem('ewin_analytics');
        console.log('Analytics events cleared');
    },

    // Generate analytics report
    generateReport() {
        const events = this.getStoredEvents();
        const report = {
            totalEvents: events.length,
            eventTypes: {},
            pageViews: events.filter(e => e.event === 'page_view').length,
            formSubmissions: events.filter(e => e.event === 'form_submit').length,
            clicks: events.filter(e => e.event === 'click').length,
            averageTimeOnPage: 0,
            maxScrollDepth: 0
        };

        // Count event types
        events.forEach(event => {
            report.eventTypes[event.event] = (report.eventTypes[event.event] || 0) + 1;
        });

        // Calculate averages
        const timeEvents = events.filter(e => e.event === 'page_unload' && e.timeOnPage);
        if (timeEvents.length > 0) {
            report.averageTimeOnPage = Math.round(
                timeEvents.reduce((sum, e) => sum + e.timeOnPage, 0) / timeEvents.length
            );
        }

        // Max scroll depth
        const scrollEvents = events.filter(e => e.event === 'page_unload' && e.maxScrollDepth);
        if (scrollEvents.length > 0) {
            report.maxScrollDepth = Math.max(...scrollEvents.map(e => e.maxScrollDepth));
        }

        return report;
    },

    // Throttle function for performance
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// Initialize analytics when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => Analytics.init());
} else {
    Analytics.init();
}

// Export for use in other modules
window.analytics = Analytics;
