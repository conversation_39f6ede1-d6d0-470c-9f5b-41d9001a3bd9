<!DOCTYPE html>
<html lang="en-AU">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - EWIN ELECTRICAL</title>
    <meta name="description" content="Thank you for contacting EWIN ELECTRICAL. We'll be in touch soon to discuss your electrical service needs.">
    <link rel="canonical" href="https://ewinelectrical.com.au/thanks.html">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/main.css">
    
    <!-- Favicon -->
    <link rel="icon" href="/assets/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header" role="banner">
        <div class="header__container">
            <div class="header__logo">
                <a href="/" aria-label="EWIN ELECTRICAL - Home">
                    <img src="/assets/logo.png" alt="EWIN ELECTRICAL Logo" width="120" height="60">
                </a>
            </div>
            
            <nav class="header__nav" role="navigation" aria-label="Main navigation">
                <ul class="header__nav-list">
                    <li><a href="/" class="header__nav-link">Home</a></li>
                    <li><a href="/services.html" class="header__nav-link">Services</a></li>
                    <li><a href="/pricing.html" class="header__nav-link">Pricing</a></li>
                    <li><a href="/contact.html" class="header__nav-link">Contact</a></li>
                </ul>
            </nav>
            
            <div class="header__cta">
                <a href="tel:+61432834236" class="btn btn--secondary btn--small">Call Now</a>
                <button class="btn btn--primary btn--small" data-calendly-popup>Book Now</button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main role="main">
        <section class="thanks-page">
            <div class="container">
                <div class="thanks-page__content">
                    <div class="thanks-page__icon">✅</div>
                    <h1 class="thanks-page__title">Thank You!</h1>
                    <p class="thanks-page__message">
                        We've received your enquiry and will be in touch within 2 hours during business hours, 
                        or first thing the next business day.
                    </p>
                    
                    <div class="thanks-page__details" id="enquiry-details">
                        <!-- Details will be populated by JavaScript from URL parameters -->
                    </div>
                    
                    <div class="thanks-page__next-steps">
                        <h2>What happens next?</h2>
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="process-step__number">1</div>
                                <div class="process-step__content">
                                    <h3>We'll Call You</h3>
                                    <p>Our team will contact you to discuss your requirements and answer any questions.</p>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="process-step__number">2</div>
                                <div class="process-step__content">
                                    <h3>Schedule Visit</h3>
                                    <p>We'll arrange a convenient time for an on-site assessment and quote.</p>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="process-step__number">3</div>
                                <div class="process-step__content">
                                    <h3>Complete Work</h3>
                                    <p>Professional installation with clean-up and final sign-off.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="thanks-page__actions">
                        <a href="/" class="btn btn--primary">Return Home</a>
                        <a href="/services.html" class="btn btn--secondary">View Services</a>
                        <button class="btn btn--secondary" data-calendly-popup>Book Appointment</button>
                    </div>
                    
                    <div class="thanks-page__emergency">
                        <h3>Need Urgent Help?</h3>
                        <p>For electrical emergencies, don't wait - call us now.</p>
                        <a href="tel:+61432834236" class="btn btn--primary btn--large">
                            Emergency: 0432 834 236
                        </a>
                    </div>
                    
                    <div class="thanks-page__contact">
                        <h3>Contact Information</h3>
                        <p>
                            <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a><br>
                            <strong>Phone:</strong> <a href="tel:+61432834236">0432 834 236</a>
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="footer__container">
            <div class="footer__content">
                <div class="footer__section">
                    <h3>Contact</h3>
                    <p>
                        <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        <a href="tel:+61432834236">0432 834 236</a>
                    </p>
                </div>
                
                <div class="footer__section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/services.html">Services</a></li>
                        <li><a href="/pricing.html">Pricing</a></li>
                        <li><a href="/contact.html">Contact</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer__bottom">
                <p>&copy; <span id="current-year">2025</span> EWIN ELECTRICAL. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/js/app.js" defer></script>
    <script src="https://assets.calendly.com/assets/external/widget.js" defer></script>
    
    <script>
        // Populate enquiry details from URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const detailsContainer = document.getElementById('enquiry-details');
            
            if (urlParams.has('name') || urlParams.has('service') || urlParams.has('suburb')) {
                let detailsHTML = '<div class="enquiry-summary"><h3>Your Enquiry Details:</h3><ul>';
                
                if (urlParams.get('name')) {
                    detailsHTML += `<li><strong>Name:</strong> ${urlParams.get('name')}</li>`;
                }
                if (urlParams.get('service')) {
                    detailsHTML += `<li><strong>Service:</strong> ${urlParams.get('service')}</li>`;
                }
                if (urlParams.get('suburb')) {
                    detailsHTML += `<li><strong>Suburb:</strong> ${urlParams.get('suburb')}</li>`;
                }
                if (urlParams.get('phone')) {
                    detailsHTML += `<li><strong>Phone:</strong> ${urlParams.get('phone')}</li>`;
                }
                
                detailsHTML += '</ul></div>';
                detailsContainer.innerHTML = detailsHTML;
            }
        });
    </script>
</body>
</html>
