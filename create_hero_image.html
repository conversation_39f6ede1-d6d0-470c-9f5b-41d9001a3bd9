<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Professional Electrician Hero Image</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0A1628;
            font-family: 'Inter', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .hero-image {
            width: 600px;
            height: 400px;
            background: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 25px rgba(15, 18, 32, 0.3);
        }
        
        .electrical-panel {
            position: absolute;
            right: 40px;
            top: 50px;
            width: 200px;
            height: 300px;
            background: linear-gradient(145deg, #374151, #4B5563);
            border-radius: 8px;
            border: 2px solid #6B7280;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .panel-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            padding: 20px;
            height: 100%;
        }
        
        .circuit-breaker {
            background: linear-gradient(145deg, #1F2937, #374151);
            border-radius: 4px;
            border: 1px solid #4B5563;
            position: relative;
        }
        
        .circuit-breaker::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 16px;
            background: #EF4444;
            border-radius: 2px;
        }
        
        .circuit-breaker:nth-child(odd)::before {
            background: #10B981;
        }
        
        .electrician {
            position: absolute;
            left: 60px;
            bottom: 40px;
            width: 180px;
            height: 280px;
        }
        
        .electrician-body {
            width: 80px;
            height: 120px;
            background: linear-gradient(145deg, #2563EB, #3B82F6);
            border-radius: 8px;
            position: absolute;
            bottom: 80px;
            left: 50px;
        }
        
        .electrician-head {
            width: 50px;
            height: 50px;
            background: #F3E8FF;
            border-radius: 50%;
            position: absolute;
            bottom: 200px;
            left: 65px;
            border: 3px solid #FBBF24;
        }
        
        .hard-hat {
            width: 60px;
            height: 30px;
            background: linear-gradient(145deg, #FBBF24, #F59E0B);
            border-radius: 30px 30px 8px 8px;
            position: absolute;
            top: -15px;
            left: -5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .safety-vest {
            position: absolute;
            top: 10px;
            left: -10px;
            right: -10px;
            height: 40px;
            background: linear-gradient(145deg, #F59E0B, #FBBF24);
            border-radius: 4px;
        }
        
        .reflective-stripe {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background: #E5E7EB;
            transform: translateY(-50%);
        }
        
        .arm {
            width: 25px;
            height: 80px;
            background: linear-gradient(145deg, #2563EB, #3B82F6);
            border-radius: 12px;
            position: absolute;
            bottom: 120px;
        }
        
        .arm.left {
            left: 25px;
            transform: rotate(-30deg);
        }
        
        .arm.right {
            right: 25px;
            transform: rotate(45deg);
        }
        
        .hand {
            width: 20px;
            height: 20px;
            background: #F3E8FF;
            border-radius: 50%;
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .tool {
            width: 3px;
            height: 40px;
            background: #6B7280;
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .legs {
            width: 20px;
            height: 80px;
            background: #1F2937;
            border-radius: 8px;
            position: absolute;
            bottom: 0;
        }
        
        .legs.left {
            left: 55px;
        }
        
        .legs.right {
            right: 55px;
        }
        
        .electrical-sparks {
            position: absolute;
            right: 80px;
            top: 120px;
        }
        
        .spark {
            width: 4px;
            height: 4px;
            background: #60A5FA;
            border-radius: 50%;
            position: absolute;
            animation: sparkle 2s infinite;
        }
        
        .spark:nth-child(1) { top: 0; left: 0; animation-delay: 0s; }
        .spark:nth-child(2) { top: 10px; left: 15px; animation-delay: 0.3s; }
        .spark:nth-child(3) { top: -5px; left: 25px; animation-delay: 0.6s; }
        .spark:nth-child(4) { top: 15px; left: 35px; animation-delay: 0.9s; }
        
        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1.5); }
        }
        
        .wires {
            position: absolute;
            right: 120px;
            top: 100px;
            width: 100px;
            height: 200px;
        }
        
        .wire {
            position: absolute;
            width: 2px;
            border-radius: 1px;
        }
        
        .wire.red {
            background: #EF4444;
            height: 80px;
            top: 20px;
            left: 10px;
            transform: rotate(15deg);
        }
        
        .wire.blue {
            background: #3B82F6;
            height: 90px;
            top: 30px;
            left: 20px;
            transform: rotate(-10deg);
        }
        
        .wire.green {
            background: #10B981;
            height: 70px;
            top: 40px;
            left: 30px;
            transform: rotate(20deg);
        }
        
        .wire.yellow {
            background: #F59E0B;
            height: 85px;
            top: 25px;
            left: 40px;
            transform: rotate(-5deg);
        }
        
        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 20% 30%, #60A5FA 2px, transparent 2px),
                radial-gradient(circle at 80% 70%, #60A5FA 1px, transparent 1px),
                radial-gradient(circle at 40% 80%, #60A5FA 1.5px, transparent 1.5px);
            background-size: 50px 50px, 30px 30px, 40px 40px;
        }
        
        .company-badge {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: 600;
            color: #1F2937;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="hero-image">
        <div class="background-pattern"></div>
        
        <!-- Electrical Panel -->
        <div class="electrical-panel">
            <div class="panel-grid">
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
                <div class="circuit-breaker"></div>
            </div>
        </div>
        
        <!-- Wires -->
        <div class="wires">
            <div class="wire red"></div>
            <div class="wire blue"></div>
            <div class="wire green"></div>
            <div class="wire yellow"></div>
        </div>
        
        <!-- Electrical Sparks -->
        <div class="electrical-sparks">
            <div class="spark"></div>
            <div class="spark"></div>
            <div class="spark"></div>
            <div class="spark"></div>
        </div>
        
        <!-- Electrician Figure -->
        <div class="electrician">
            <div class="electrician-head">
                <div class="hard-hat"></div>
            </div>
            <div class="electrician-body">
                <div class="safety-vest">
                    <div class="reflective-stripe"></div>
                </div>
            </div>
            <div class="arm left">
                <div class="hand">
                    <div class="tool"></div>
                </div>
            </div>
            <div class="arm right">
                <div class="hand"></div>
            </div>
            <div class="legs left"></div>
            <div class="legs right"></div>
        </div>
        
        <div class="company-badge">EWIN ELECTRICAL</div>
    </div>
</body>
</html>
