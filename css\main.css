/* ===================================
   EWIN ELECTRICAL - Main Stylesheet
   ================================== */

/* CSS Custom Properties (Variables) */
:root {
  /* Brand Colors */
  --navy-950: #071226;
  --navy-900: #0B1733;
  --electric-700: #1F7BE8;
  --electric-500: #3AA8FF;
  --electric-300: #7FD6FF;
  --ink: #0F1220;
  --white: #FFFFFF;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--electric-700), var(--electric-300));
  --gradient-secondary: linear-gradient(135deg, var(--navy-900), var(--navy-950));
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Fluid Typography */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --container-padding: var(--space-md);
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Reset & Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

body {
  font-family: var(--font-family);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--white);
  background-color: var(--navy-950);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--space-md);
  color: var(--white);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin-bottom: var(--space-md);
  color: rgba(255, 255, 255, 0.9);
}

a {
  color: var(--electric-300);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover,
a:focus {
  color: var(--electric-500);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--electric-500);
  outline-offset: 2px;
}

/* Lists */
ul, ol {
  margin-bottom: var(--space-md);
  padding-left: var(--space-lg);
}

li {
  margin-bottom: var(--space-xs);
  color: rgba(255, 255, 255, 0.9);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Layout Utilities */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.section {
  padding: var(--space-4xl) 0;
}

.section-title {
  text-align: center;
  margin-bottom: var(--space-3xl);
  font-size: var(--text-3xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  font-family: inherit;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(63, 168, 255, 0.3);
}

.btn--primary {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: transparent;
}

.btn--primary:hover,
.btn--primary:focus {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(63, 168, 255, 0.4);
  text-decoration: none;
  color: var(--white);
}

.btn--secondary {
  background: transparent;
  color: var(--electric-300);
  border-color: var(--electric-300);
}

.btn--secondary:hover,
.btn--secondary:focus {
  background: var(--electric-300);
  color: var(--navy-950);
  text-decoration: none;
}

.btn--small {
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-sm);
}

.btn--large {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--text-lg);
}

/* Header */
.header {
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  background: rgba(7, 18, 38, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md) var(--container-padding);
  max-width: var(--container-max-width);
  margin: 0 auto;
  gap: var(--space-lg);
}

.header__logo img {
  height: 60px;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Header Contact */
.header__contact {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: var(--space-lg);
}

.header__phone {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--white);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-normal);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
}

.header__phone:hover {
  color: var(--electric-300);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.header__phone-icon {
  width: 16px;
  height: 16px;
  background: var(--electric-300);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
}

.header__phone-icon::before {
  content: '';
  width: 6px;
  height: 6px;
  background: var(--white);
  border-radius: 2px;
}

.header__phone-number {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.025em;
  white-space: nowrap;
}

.header__nav-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm);
}

.header__nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--white);
  margin: 3px 0;
  transition: var(--transition-fast);
}

.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-xl);
}

.header__nav-link {
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) 0;
  position: relative;
}

.header__nav-link:hover,
.header__nav-link:focus,
.header__nav-link--active {
  color: var(--electric-300);
  text-decoration: none;
}

.header__nav-link--active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
}

.header__cta {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

/* Hero Section */
.hero {
  padding: var(--space-4xl) 0;
  background: var(--gradient-secondary);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 50%, rgba(63, 168, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  position: relative;
  z-index: 1;
}

.hero__logo img {
  height: 75px;
  width: auto;
  margin-bottom: var(--space-lg);
}

.hero__title {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__subtitle {
  font-size: var(--text-xl);
  margin-bottom: var(--space-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.hero__trust-badges {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-badge__icon {
  width: 20px;
  height: 20px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.trust-badge__icon::before {
  content: '✓';
  color: var(--white);
  font-size: 12px;
  font-weight: bold;
}

.trust-badge__text {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--white);
}

.hero__cta {
  display: flex;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.hero__image {
  position: relative;
}

.hero__image img {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
}

/* Electrical Hero Visual */
.electrical-hero-visual {
  width: 100%;
  max-width: 600px;
  height: 400px;
  background: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
  border-radius: var(--border-radius-lg);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  margin: 0 auto;
}

.electrical-panel {
  position: absolute;
  right: 40px;
  top: 50px;
  width: 200px;
  height: 300px;
  background: linear-gradient(145deg, #374151, #4B5563);
  border-radius: 8px;
  border: 2px solid #6B7280;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.panel-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  padding: 20px;
  height: 100%;
}

.circuit-breaker {
  background: linear-gradient(145deg, #1F2937, #374151);
  border-radius: 4px;
  border: 1px solid #4B5563;
  position: relative;
}

.circuit-breaker::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 16px;
  background: #EF4444;
  border-radius: 2px;
}

.circuit-breaker:nth-child(odd)::before {
  background: #10B981;
}

.electrician-figure {
  position: absolute;
  left: 60px;
  bottom: 40px;
  width: 180px;
  height: 280px;
}

.electrician-body {
  width: 80px;
  height: 120px;
  background: linear-gradient(145deg, #2563EB, #3B82F6);
  border-radius: 8px;
  position: absolute;
  bottom: 80px;
  left: 50px;
}

.electrician-head {
  width: 50px;
  height: 50px;
  background: #F3E8FF;
  border-radius: 50%;
  position: absolute;
  bottom: 200px;
  left: 65px;
  border: 3px solid #FBBF24;
}

.hard-hat {
  width: 60px;
  height: 30px;
  background: linear-gradient(145deg, #FBBF24, #F59E0B);
  border-radius: 30px 30px 8px 8px;
  position: absolute;
  top: -15px;
  left: -5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.safety-vest {
  position: absolute;
  top: 10px;
  left: -10px;
  right: -10px;
  height: 40px;
  background: linear-gradient(145deg, #F59E0B, #FBBF24);
  border-radius: 4px;
}

.reflective-stripe {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background: #E5E7EB;
  transform: translateY(-50%);
}

.arm {
  width: 25px;
  height: 80px;
  background: linear-gradient(145deg, #2563EB, #3B82F6);
  border-radius: 12px;
  position: absolute;
  bottom: 120px;
}

.arm.left {
  left: 25px;
  transform: rotate(-30deg);
}

.arm.right {
  right: 25px;
  transform: rotate(45deg);
}

.hand {
  width: 20px;
  height: 20px;
  background: #F3E8FF;
  border-radius: 50%;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.tool {
  width: 3px;
  height: 40px;
  background: #6B7280;
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.legs {
  width: 20px;
  height: 80px;
  background: #1F2937;
  border-radius: 8px;
  position: absolute;
  bottom: 0;
}

.legs.left {
  left: 55px;
}

.legs.right {
  right: 55px;
}

.electrical-wires {
  position: absolute;
  right: 120px;
  top: 100px;
  width: 100px;
  height: 200px;
}

.wire {
  position: absolute;
  width: 2px;
  border-radius: 1px;
}

.wire.red {
  background: #EF4444;
  height: 80px;
  top: 20px;
  left: 10px;
  transform: rotate(15deg);
}

.wire.blue {
  background: #3B82F6;
  height: 90px;
  top: 30px;
  left: 20px;
  transform: rotate(-10deg);
}

.wire.green {
  background: #10B981;
  height: 70px;
  top: 40px;
  left: 30px;
  transform: rotate(20deg);
}

.wire.yellow {
  background: #F59E0B;
  height: 85px;
  top: 25px;
  left: 40px;
  transform: rotate(-5deg);
}

.electrical-sparks {
  position: absolute;
  right: 80px;
  top: 120px;
}

.spark {
  width: 4px;
  height: 4px;
  background: #60A5FA;
  border-radius: 50%;
  position: absolute;
  animation: sparkle 2s infinite;
}

.spark:nth-child(1) {
  top: 0;
  left: 0;
  animation-delay: 0s;
}

.spark:nth-child(2) {
  top: 10px;
  left: 15px;
  animation-delay: 0.3s;
}

.spark:nth-child(3) {
  top: -5px;
  left: 25px;
  animation-delay: 0.6s;
}

.spark:nth-child(4) {
  top: 15px;
  left: 35px;
  animation-delay: 0.9s;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

.background-circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image:
    radial-gradient(circle at 20% 30%, #60A5FA 2px, transparent 2px),
    radial-gradient(circle at 80% 70%, #60A5FA 1px, transparent 1px),
    radial-gradient(circle at 40% 80%, #60A5FA 1.5px, transparent 1.5px);
  background-size: 50px 50px, 30px 30px, 40px 40px;
}

/* Responsive adjustments for electrical hero */
@media (max-width: 768px) {
  .electrical-hero-visual {
    height: 300px;
  }

  .electrical-panel {
    width: 150px;
    height: 200px;
    right: 20px;
    top: 30px;
  }

  .panel-grid {
    padding: 15px;
    gap: 6px;
  }

  .electrician-figure {
    left: 30px;
    bottom: 20px;
    transform: scale(0.8);
  }

  .electrical-wires {
    right: 80px;
    top: 80px;
    transform: scale(0.8);
  }

  .electrical-sparks {
    right: 60px;
    top: 100px;
    transform: scale(0.8);
  }
}

/* Why Choose Us Section */
.why-choose-us {
  padding: var(--space-4xl) 0;
  background: var(--navy-900);
}

.why-choose-us__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  padding: var(--space-2xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(7, 18, 38, 0.15);
}

.feature-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(7, 18, 38, 0.2), 0 4px 10px rgba(58, 168, 255, 0.1);
}

.feature-card__icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  margin: 0 auto var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.feature-card__icon::before {
  content: '';
  width: 24px;
  height: 24px;
  background: var(--white);
  border-radius: 4px;
}

.feature-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--electric-300);
}

.feature-card__description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header__nav {
    display: none;
  }

  .header__nav-toggle {
    display: flex;
  }

  .header__contact {
    margin-right: var(--space-md);
  }

  .header__phone-number {
    display: none;
  }

  .header__phone {
    padding: var(--space-xs) var(--space-sm);
    min-width: auto;
  }
  
  .hero__container {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero__trust-badges {
    justify-content: center;
  }
  
  .hero__cta {
    justify-content: center;
  }
  
  .why-choose-us__grid {
    grid-template-columns: 1fr;
  }
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab:hover,
.fab:focus {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(63, 168, 255, 0.4);
}

.fab__icon {
  font-size: var(--text-xs);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.5px;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: var(--space-xl);
  left: var(--space-xl);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(10px);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover,
.back-to-top:focus {
  background: var(--electric-300);
  color: var(--navy-950);
  transform: translateY(-2px);
}

.back-to-top__icon {
  font-size: var(--text-lg);
  color: var(--white);
  transition: color var(--transition-fast);
}

.back-to-top:hover .back-to-top__icon,
.back-to-top:focus .back-to-top__icon {
  color: var(--navy-950);
}

/* Modal System */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(5px);
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal__content {
  background: var(--navy-900);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-xl);
}

.modal__close {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: none;
  border: none;
  font-size: var(--text-2xl);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: color var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.modal__close:hover,
.modal__close:focus {
  color: var(--electric-300);
  background: rgba(255, 255, 255, 0.1);
}

/* Form Styles */
.form {
  max-width: 600px;
  margin: 0 auto;
}

.form__group {
  margin-bottom: var(--space-lg);
}

.form__label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-medium);
  color: var(--white);
}

.form__input,
.form__select,
.form__textarea {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.05);
  color: var(--white);
  font-family: inherit;
  font-size: var(--text-base);
  transition: border-color var(--transition-fast);
}

.form__input:focus,
.form__select:focus,
.form__textarea:focus {
  outline: none;
  border-color: var(--electric-500);
  box-shadow: 0 0 0 3px rgba(63, 168, 255, 0.2);
}

.form__input::placeholder,
.form__textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form__textarea {
  resize: vertical;
  min-height: 120px;
}

.form__error {
  color: #ff6b6b;
  font-size: var(--text-sm);
  margin-top: var(--space-xs);
  display: none;
}

.form__error.visible {
  display: block;
}

.form__group.error .form__input,
.form__group.error .form__select,
.form__group.error .form__textarea {
  border-color: #ff6b6b;
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: var(--space-xl);
  right: var(--space-xl);
  background: var(--navy-900);
  color: var(--white);
  padding: var(--space-lg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-tooltip);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  border-left: 4px solid var(--electric-500);
  max-width: 400px;
}

.toast.visible {
  transform: translateX(0);
}

.toast--success {
  border-left-color: #4ade80;
}

.toast--error {
  border-left-color: #ff6b6b;
}

.toast--warning {
  border-left-color: #fbbf24;
}

/* Accordion */
.accordion {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.accordion__item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.accordion__item:last-child {
  border-bottom: none;
}

.accordion__header {
  width: 100%;
  padding: var(--space-lg);
  background: rgba(255, 255, 255, 0.05);
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--white);
  transition: background-color var(--transition-fast);
}

.accordion__header:hover,
.accordion__header:focus {
  background: rgba(255, 255, 255, 0.08);
}

.accordion__icon {
  transition: transform var(--transition-fast);
}

.accordion__item.active .accordion__icon {
  transform: rotate(180deg);
}

.accordion__content {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.accordion__item.active .accordion__content {
  max-height: 500px;
}

.accordion__body {
  padding: var(--space-lg);
  color: rgba(255, 255, 255, 0.9);
}

/* Footer */
.footer {
  background: var(--navy-950);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-4xl) 0 var(--space-xl);
}

.footer__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2xl);
  margin-bottom: var(--space-2xl);
}

.footer__section h3 {
  color: var(--electric-300);
  margin-bottom: var(--space-md);
  font-size: var(--text-lg);
}

.footer__section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__section li {
  margin-bottom: var(--space-sm);
}

.footer__section a {
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-fast);
}

.footer__section a:hover,
.footer__section a:focus {
  color: var(--electric-300);
  text-decoration: none;
}

.footer__bottom {
  text-align: center;
  padding-top: var(--space-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--text-sm);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.service-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  transition: all var(--transition-normal);
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(7, 18, 38, 0.15);
}

.service-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(7, 18, 38, 0.2), 0 4px 10px rgba(58, 168, 255, 0.1);
}

.service-card__icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
  display: block;
  color: var(--electric-300);
}

.service-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--white);
}

.service-card__description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-lg);
  line-height: 1.6;
}

.service-card__cta {
  color: var(--electric-300);
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
}

.service-card__cta::after {
  content: '→';
  transition: transform var(--transition-fast);
}

.service-card:hover .service-card__cta::after {
  transform: translateX(4px);
}

/* Process Steps */
.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.process-step {
  display: flex;
  gap: var(--space-lg);
  align-items: flex-start;
}

.process-step__number {
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
  color: var(--white);
  flex-shrink: 0;
}

.process-step__content h3 {
  margin-bottom: var(--space-sm);
  color: var(--electric-300);
}

.process-step__content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
}

/* Testimonials */
.testimonials {
  padding: var(--space-4xl) 0;
  background: var(--navy-900);
}

.testimonials__carousel {
  position: relative;
  overflow: hidden;
  margin-top: var(--space-3xl);
}

.testimonials__track {
  display: flex;
  transition: transform var(--transition-slow);
}

.testimonial-card {
  flex: 0 0 100%;
  padding: var(--space-2xl);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0 var(--space-md);
  text-align: center;
}

.testimonial-card__quote {
  font-size: var(--text-lg);
  font-style: italic;
  margin-bottom: var(--space-lg);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.testimonial-card__author {
  font-weight: var(--font-weight-semibold);
  color: var(--electric-300);
  margin-bottom: var(--space-sm);
}

.testimonial-card__location {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--text-sm);
}

.testimonials__controls {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin-top: var(--space-xl);
}

.testimonials__btn {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  transition: all var(--transition-fast);
}

.testimonials__btn:hover,
.testimonials__btn:focus {
  background: var(--electric-300);
  color: var(--navy-950);
}

.testimonials__dots {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  margin-top: var(--space-lg);
}

.testimonials__dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.testimonials__dot.active {
  background: var(--electric-300);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-3xl);
}

.gallery-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.gallery-item:hover {
  transform: scale(1.02);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-item__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-item__overlay {
  opacity: 1;
}

.gallery-item__icon {
  font-size: var(--text-3xl);
  color: var(--white);
}

/* Pricing Tables */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.pricing-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  position: relative;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(7, 18, 38, 0.15);
}

.pricing-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(7, 18, 38, 0.2), 0 4px 10px rgba(58, 168, 255, 0.1);
}

.pricing-card--featured {
  border-color: var(--electric-500);
  transform: scale(1.05);
}

.pricing-card--featured::before {
  content: 'Most Popular';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--border-radius);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
}

.pricing-card:hover {
  transform: translateY(-5px);
  border-color: var(--electric-300);
}

.pricing-card--featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-card__title {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-md);
  color: var(--electric-300);
}

.pricing-card__price {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-lg);
  color: var(--white);
}

.pricing-card__features {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-2xl) 0;
}

.pricing-card__feature {
  padding: var(--space-sm) 0;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.pricing-card__feature::before {
  content: '✓';
  color: var(--electric-300);
  font-weight: var(--font-weight-bold);
}

/* Error Pages */
.error-page {
  padding: var(--space-4xl) 0;
  text-align: center;
}

.error-page__content {
  max-width: 600px;
  margin: 0 auto;
}

.error-page__icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-lg);
  display: block;
  color: var(--electric-300);
}

.error-page__title {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-page__message {
  font-size: var(--text-lg);
  margin-bottom: var(--space-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.error-page__actions {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.error-page__help {
  margin-bottom: var(--space-2xl);
}

.error-page__help h2 {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--electric-300);
}

.error-page__help ul {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.error-page__emergency {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: var(--border-radius);
  padding: var(--space-xl);
  margin-top: var(--space-2xl);
}

/* Thanks Page */
.thanks-page {
  padding: var(--space-4xl) 0;
  text-align: center;
}

.thanks-page__content {
  max-width: 700px;
  margin: 0 auto;
}

.thanks-page__icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-lg);
  display: block;
  color: #4ade80;
}

.thanks-page__title {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
  color: var(--electric-300);
}

.thanks-page__message {
  font-size: var(--text-lg);
  margin-bottom: var(--space-2xl);
  color: rgba(255, 255, 255, 0.9);
}

.thanks-page__details {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  padding: var(--space-xl);
  margin-bottom: var(--space-2xl);
  text-align: left;
}

.enquiry-summary h3 {
  color: var(--electric-300);
  margin-bottom: var(--space-md);
}

.enquiry-summary ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.enquiry-summary li {
  padding: var(--space-sm) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.enquiry-summary li:last-child {
  border-bottom: none;
}

.thanks-page__next-steps {
  margin-bottom: var(--space-2xl);
}

.thanks-page__next-steps h2 {
  color: var(--electric-300);
  margin-bottom: var(--space-xl);
}

.thanks-page__actions {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.thanks-page__emergency,
.thanks-page__contact {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
}

.thanks-page__emergency h3,
.thanks-page__contact h3 {
  color: var(--electric-300);
  margin-bottom: var(--space-md);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    gap: var(--space-2xl);
  }

  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .pricing-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .pricing-card--featured {
    transform: none;
  }

  .pricing-card--featured:hover {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  :root {
    --container-padding: var(--space-lg);
  }

  .header__nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(7, 18, 38, 0.98);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .header__nav.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .header__nav-list {
    flex-direction: column;
    padding: var(--space-lg);
    gap: var(--space-md);
  }

  .header__nav-toggle {
    display: flex;
  }

  .header__nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .header__nav-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .header__nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero__container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-2xl);
  }

  .hero__trust-badges {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .hero__cta {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .why-choose-us__grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
  }

  .testimonial-card {
    margin: 0;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .fab {
    bottom: var(--space-lg);
    right: var(--space-lg);
    width: 50px;
    height: 50px;
  }

  .back-to-top {
    bottom: var(--space-lg);
    left: var(--space-lg);
    width: 40px;
    height: 40px;
  }

  .modal__content {
    margin: var(--space-lg);
    padding: var(--space-lg);
  }

  .error-page__actions {
    flex-direction: column;
    align-items: center;
  }

  .thanks-page__actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: var(--space-md);
  }

  .header__container {
    padding: var(--space-sm) var(--container-padding);
  }

  .header__logo img {
    height: 50px;
  }

  .hero {
    padding: var(--space-2xl) 0;
  }

  .hero__logo img {
    height: 60px;
  }

  .section {
    padding: var(--space-2xl) 0;
  }

  .feature-card,
  .service-card,
  .testimonial-card,
  .pricing-card {
    padding: var(--space-lg);
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .gallery-item img {
    height: 200px;
  }

  .toast {
    top: var(--space-md);
    right: var(--space-md);
    left: var(--space-md);
    max-width: none;
  }
}

/* Print Styles */
@media print {
  .header,
  .fab,
  .back-to-top,
  .modal,
  .toast {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .hero {
    background: white !important;
  }

  .hero__title,
  .section-title {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: initial !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --navy-950: #000000;
    --navy-900: #1a1a1a;
    --electric-700: #0066ff;
    --electric-500: #0080ff;
    --electric-300: #00aaff;
    --white: #ffffff;
  }

  .btn--primary {
    border: 2px solid var(--white);
  }

  .btn--secondary {
    background: var(--white);
    color: var(--navy-950);
  }
}

/* Focus Visible for Better Accessibility */
.btn:focus-visible,
.header__nav-link:focus-visible,
.form__input:focus-visible,
.form__select:focus-visible,
.form__textarea:focus-visible {
  outline: 3px solid var(--electric-500);
  outline-offset: 2px;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--electric-300);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Lazy Loading */
.lazy-load {
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.lazy-load.loaded {
  opacity: 1;
}

/* Reveal Animations */
.reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--transition-slow);
}

.reveal.visible {
  opacity: 1;
  transform: translateY(0);
}

@media (prefers-reduced-motion: reduce) {
  .reveal {
    opacity: 1;
    transform: none;
    transition: none;
  }
}

/* Additional Components */
.suburbs-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-sm);
}

.suburbs-list li {
  margin-bottom: 0;
}

.suburbs-list a {
  display: block;
  padding: var(--space-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  text-align: center;
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.suburbs-list a:hover,
.suburbs-list a:focus {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
}

.service-area__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: start;
  margin-top: var(--space-3xl);
}

.service-area__map {
  position: relative;
}

.map-placeholder {
  cursor: pointer;
  transition: transform var(--transition-normal);
}

.map-placeholder:hover {
  transform: scale(1.02);
}

.service-area__suburbs h3 {
  color: var(--electric-300);
  margin-bottom: var(--space-lg);
}

.form__row {
  margin-bottom: var(--space-lg);
}

.form__help {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.7);
  margin-top: var(--space-xs);
}

.calendly-inline-widget {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Enhanced Mobile Styles */
@media (max-width: 768px) {
  .service-area__grid {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .form__row {
    display: block !important;
  }

  .form__row .form__group {
    margin-bottom: var(--space-lg);
  }

  .suburbs-list {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .calendly-inline-widget {
    height: 500px !important;
  }
}

@media (max-width: 480px) {
  .suburbs-list {
    grid-template-columns: 1fr 1fr;
  }

  .modal__content {
    margin: var(--space-sm);
    padding: var(--space-md);
    max-height: 90vh;
  }
}

/* Page Header */
.page-header {
  padding: var(--space-4xl) 0 var(--space-2xl);
  background: var(--gradient-secondary);
  text-align: center;
}

.page-header__title {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header__subtitle {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-2xl);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.page-header__cta {
  display: flex;
  gap: var(--space-lg);
  justify-content: center;
  flex-wrap: wrap;
}

/* Breadcrumb */
.breadcrumb {
  list-style: none;
  padding: 0;
  margin: 0 0 var(--space-lg) 0;
  display: flex;
  gap: var(--space-sm);
  justify-content: center;
  font-size: var(--text-sm);
}

.breadcrumb li:not(:last-child)::after {
  content: '›';
  margin-left: var(--space-sm);
  color: rgba(255, 255, 255, 0.5);
}

.breadcrumb a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
}

.breadcrumb a:hover {
  color: var(--electric-300);
}

.breadcrumb li[aria-current="page"] {
  color: var(--electric-300);
  font-weight: var(--font-weight-medium);
}

/* Service Filter */
.service-filter {
  padding: var(--space-xl) 0;
  background: var(--navy-900);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-controls {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-sm) var(--space-lg);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  color: rgba(255, 255, 255, 0.8);
  font-family: inherit;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn:focus {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-color: rgba(255, 255, 255, 0.3);
}

.filter-btn.active {
  background: var(--gradient-primary);
  border-color: transparent;
  color: var(--white);
}

/* Feature Tags */
.service-card__features {
  display: flex;
  gap: var(--space-xs);
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
}

.feature-tag {
  padding: var(--space-xs) var(--space-sm);
  background: rgba(63, 168, 255, 0.2);
  color: var(--electric-300);
  border-radius: var(--border-radius);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(63, 168, 255, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Mobile Styles for Services */
@media (max-width: 768px) {
  .page-header {
    padding: var(--space-2xl) 0;
  }

  .page-header__cta {
    flex-direction: column;
    align-items: center;
  }

  .filter-controls {
    gap: var(--space-sm);
  }

  .filter-btn {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-xs);
  }

  .service-card__features {
    gap: var(--space-xs);
  }

  .feature-tag {
    font-size: 0.7rem;
    padding: 2px var(--space-xs);
  }
}

/* Pricing Specific Styles */
.pricing-note {
  text-align: center;
  margin-top: var(--space-3xl);
  padding: var(--space-lg);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pricing-note p {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--text-sm);
  margin-bottom: 0;
}

/* Service Rates */
.rates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.rate-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  transition: all var(--transition-normal);
}

.rate-card:hover {
  transform: translateY(-5px);
  border-color: var(--electric-300);
}

.rate-card--emergency {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.rate-card--emergency:hover {
  border-color: #ff5252;
}

.rate-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--electric-300);
}

.rate-card--emergency .rate-card__title {
  color: #ff6b6b;
}

.rate-card__time {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: var(--space-lg);
}

.rate-card__price {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  margin-bottom: var(--space-lg);
}

.rate-card__includes {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rate-card__includes li {
  padding: var(--space-sm) 0;
  color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  justify-content: center;
}

.rate-card__includes li::before {
  content: '✓';
  color: var(--electric-300);
  font-weight: var(--font-weight-bold);
}

.rate-card--emergency .rate-card__includes li::before {
  color: #ff6b6b;
}

/* Enhanced Mobile Styles for Pricing */
@media (max-width: 768px) {
  .rates-grid {
    grid-template-columns: 1fr;
  }

  .rate-card {
    padding: var(--space-lg);
  }

  .rate-card__price {
    font-size: var(--text-2xl);
  }
}

/* About Page Styles */
.about-intro__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  margin-top: var(--space-3xl);
}

.about-intro__content h2 {
  color: var(--electric-300);
  margin-bottom: var(--space-lg);
}

.about-intro__cta {
  display: flex;
  gap: var(--space-lg);
  margin-top: var(--space-xl);
  flex-wrap: wrap;
}

.about-intro__image img {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Credentials */
.credentials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.credential-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  padding: var(--space-2xl);
  text-align: center;
  transition: transform var(--transition-normal);
}

.credential-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
}

.credential-card__icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  margin: 0 auto var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.credential-card__icon::before {
  content: '';
  width: 24px;
  height: 24px;
  background: var(--white);
  border-radius: 4px;
}

.credential-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--electric-300);
}

.credential-card__description {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-lg);
  line-height: 1.6;
}

.credential-card__details {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  padding: var(--space-md);
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.8);
  text-align: left;
  line-height: 1.8;
}

/* Values */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2xl);
  margin-top: var(--space-3xl);
}

.value-card {
  text-align: center;
  padding: var(--space-xl);
}

.value-card__icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border-radius: 10px;
  margin: 0 auto var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.value-card__icon::before {
  content: '';
  width: 20px;
  height: 20px;
  background: var(--white);
  border-radius: 3px;
}

.value-card__title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-md);
  color: var(--white);
}

.value-card__description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* Timeline */
.timeline-container {
  position: relative;
  margin-top: var(--space-3xl);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gradient-primary);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-3xl);
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-item__year {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
  color: var(--white);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.timeline-item__content {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  padding: var(--space-xl);
  margin: 0 var(--space-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline-item__content h3 {
  color: var(--electric-300);
  margin-bottom: var(--space-sm);
  font-size: var(--text-lg);
}

.timeline-item__content p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  line-height: 1.6;
}

/* Enhanced Mobile Styles for About */
@media (max-width: 768px) {
  .about-intro__grid {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .about-intro__cta {
    justify-content: center;
  }

  .credentials-grid {
    grid-template-columns: 1fr;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .timeline-container::before {
    left: 40px;
  }

  .timeline-item {
    flex-direction: row !important;
    align-items: flex-start;
  }

  .timeline-item__year {
    width: 60px;
    height: 60px;
    font-size: var(--text-base);
    margin-left: 10px;
  }

  .timeline-item__content {
    margin-left: var(--space-lg);
    margin-right: 0;
  }
}
