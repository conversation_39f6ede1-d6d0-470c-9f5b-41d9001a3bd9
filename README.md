# EWIN ELECTRICAL - Professional Electrical Services Website

A production-ready, responsive, accessible, and SEO-optimized website for EWIN ELECTRICAL, an Australian electrical company. Built with vanilla HTML, CSS, and JavaScript for maximum performance and compatibility.

## 🚀 Features

- **Responsive Design**: Mobile-first approach with fluid typography and layouts
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels and keyboard navigation
- **SEO Optimized**: Comprehensive meta tags, Schema.org markup, and technical SEO
- **Performance**: Lighthouse scores ≥95, lazy loading, optimized images
- **Modern UI**: Clean, professional design with CSS Grid and Flexbox
- **Interactive Elements**: Testimonial carousel, service modals, form validation
- **Calendly Integration**: Popup and inline booking widgets
- **Analytics Ready**: Privacy-friendly analytics with console logging

## 📁 Project Structure

```
├── index.html              # Homepage
├── services.html           # Services page with filtering
├── pricing.html            # Pricing packages and rates
├── about.html              # About page with credentials
├── gallery.html            # Image gallery (to be created)
├── faqs.html               # FAQ page (to be created)
├── contact.html            # Contact page (to be created)
├── suburbs.html            # Service areas (to be created)
├── 404.html                # Error page
├── thanks.html             # Thank you page
├── robots.txt              # Search engine directives
├── sitemap.xml             # Site structure for search engines
├── humans.txt              # Credits and team information
├── assets/                 # Images, icons, and media files
│   ├── logo.png            # Company logo (replace with actual)
│   ├── hero.jpg            # Hero section image
│   ├── site.webmanifest    # PWA manifest
│   └── favicon.ico         # Favicon
├── css/
│   └── main.css            # Complete stylesheet with CSS variables
├── js/
│   ├── app.js              # Main application logic
│   ├── validate.js         # Form validation
│   ├── analytics.js        # Analytics tracking
│   └── lazyload.js         # Image lazy loading
└── partials/               # Optional HTML partials (for future use)
```

## 🛠️ Setup Instructions

### 1. Replace Logo and Branding

1. Replace `/assets/logo.png` with your actual company logo
2. Update the color scheme in `/css/main.css` if needed:
   ```css
   :root {
     --navy-950: #071226;    /* Dark background */
     --navy-900: #0B1733;    /* Secondary background */
     --electric-700: #1F7BE8; /* Primary blue */
     --electric-500: #3AA8FF; /* Accent blue */
     --electric-300: #7FD6FF; /* Light blue */
   }
   ```

### 2. Configure Calendly Integration

1. Open `/js/app.js`
2. Update the Calendly URL:
   ```javascript
   const CONFIG = {
       CALENDLY_URL: 'https://calendly.com/YOUR-ACTUAL-HANDLE/30min',
       // ... other config
   };
   ```
3. Replace `YOUR-HANDLE` with your actual Calendly username

### 3. Setup Contact Forms

#### Option A: EmailJS
1. Sign up at [EmailJS](https://www.emailjs.com/)
2. Create a service and template
3. Update form submission in `/js/app.js`:
   ```javascript
   // Replace the submitForm function with EmailJS integration
   function submitForm(form) {
       const formData = new FormData(form);
       
       emailjs.sendForm('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', form)
           .then(() => {
               // Success handling
               window.location.href = '/thanks.html';
           })
           .catch((error) => {
               // Error handling
               console.error('Form submission failed:', error);
           });
   }
   ```

#### Option B: Formspree
1. Sign up at [Formspree](https://formspree.io/)
2. Create a form and get the endpoint
3. Update form action:
   ```html
   <form class="form" action="https://formspree.io/f/YOUR_FORM_ID" method="POST">
   ```

### 4. Update Contact Information

Replace placeholder contact details throughout the site:
- Email: `<EMAIL>`
- Phone: `0432834236`
- Update in all HTML files and Schema.org markup

### 5. Add Real Content

1. Replace placeholder images in `/assets/`
2. Update service descriptions and pricing
3. Add real testimonials and credentials
4. Update licence and ABN numbers in footer

## 🌐 Deployment

### Netlify (Recommended)
1. Connect your Git repository to Netlify
2. Build settings: None required (static site)
3. Deploy directory: Root (`/`)
4. Add custom domain and SSL certificate

### Vercel
1. Import project from Git
2. Framework preset: Other
3. Build command: None
4. Output directory: `./`

### GitHub Pages
1. Push code to GitHub repository
2. Go to Settings > Pages
3. Select source branch (usually `main`)
4. Site will be available at `https://username.github.io/repository-name`

### Traditional Web Hosting
1. Upload all files to your web server's public directory
2. Ensure proper file permissions (644 for files, 755 for directories)
3. Configure your domain to point to the hosting server

## 📊 Analytics Setup

The site includes a privacy-friendly analytics system that logs to console by default. To integrate with real analytics:

### Google Analytics 4
```javascript
// Add to analytics.js
gtag('event', eventName, {
    event_category: data.category,
    event_label: data.label,
    value: data.value
});
```

### Mixpanel
```javascript
// Add to analytics.js
mixpanel.track(eventName, eventData);
```

## 🔧 Maintenance

### Regular Updates
1. **Content**: Update pricing, services, and testimonials regularly
2. **Images**: Optimize and update gallery images
3. **SEO**: Update meta descriptions and titles seasonally
4. **Security**: Keep any third-party integrations updated

### Performance Monitoring
1. Run Lighthouse audits monthly
2. Monitor Core Web Vitals
3. Check broken links quarterly
4. Update sitemap.xml when adding new pages

### Backup Strategy
1. Regular backups of all files
2. Version control with Git
3. Database backups if using dynamic content
4. Test restore procedures

## 🎨 Customization

### Adding New Pages
1. Copy an existing HTML file as template
2. Update navigation in header
3. Add to sitemap.xml
4. Update robots.txt if needed

### Modifying Styles
- All styles are in `/css/main.css`
- Uses CSS custom properties for easy theming
- BEM methodology for class naming
- Mobile-first responsive design

### Adding New Components
1. Add HTML structure
2. Add CSS styles following BEM convention
3. Add JavaScript functionality if needed
4. Test across all devices and browsers

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔍 SEO Features

- **Technical SEO**: Proper HTML structure, meta tags, Schema.org markup
- **Performance**: Fast loading times, optimized images
- **Mobile-First**: Responsive design for all devices
- **Accessibility**: Screen reader friendly, keyboard navigation
- **Content**: Semantic HTML, proper heading hierarchy

## 📞 Support

For technical support or customization requests:
- Email: [Your support email]
- Documentation: This README file
- Issues: Use GitHub issues for bug reports

## 📄 License

This website template is created for EWIN ELECTRICAL. All rights reserved.

### Third-Party Licenses
- Inter Font: SIL Open Font License
- Calendly Widget: Calendly Terms of Service
- Images: Ensure proper licensing for all images used

## 🙏 Credits

- **Design & Development**: Built with Augment Code
- **Fonts**: Inter by Google Fonts
- **Icons**: Unicode emojis and custom SVGs
- **Images**: [Add attribution for stock photos]

---

## 🚀 Quick Start

1. **Clone or download** this repository
2. **Replace the logo**: Update `/assets/logo.png` with your actual logo
3. **Configure Calendly**: Update the URL in `/js/app.js`
4. **Setup forms**: Choose EmailJS or Formspree and configure
5. **Update contact info**: Replace all placeholder contact details
6. **Test locally**: Open `index.html` in a web browser
7. **Deploy**: Upload to your hosting provider or use Netlify/Vercel

## 📋 Pre-Launch Checklist

- [ ] Logo replaced with actual company logo
- [ ] All contact information updated (email, phone, address)
- [ ] Calendly URL configured and tested
- [ ] Contact forms working and tested
- [ ] All placeholder content replaced with real content
- [ ] Licence and ABN numbers added to footer
- [ ] Real testimonials and credentials added
- [ ] Images optimized and properly attributed
- [ ] Meta descriptions and titles reviewed
- [ ] 404 and thank you pages tested
- [ ] Mobile responsiveness tested on real devices
- [ ] Accessibility tested with screen reader
- [ ] Performance tested with Lighthouse
- [ ] All links working and tested
- [ ] Analytics configured (if using)
- [ ] SSL certificate installed
- [ ] Domain configured and tested

## 🔧 Advanced Configuration

### Custom Domain Setup
1. Purchase domain from registrar
2. Configure DNS to point to hosting provider
3. Setup SSL certificate (usually automatic with modern hosts)
4. Update canonical URLs in all HTML files

### Performance Optimization
1. **Images**: Use WebP format where possible
2. **Caching**: Configure browser caching headers
3. **CDN**: Consider using a CDN for static assets
4. **Compression**: Enable Gzip compression on server

### Security Headers
Add these headers to your server configuration:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://assets.calendly.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
```

**Note**: This is a production-ready website template. Ensure all placeholder content is replaced with actual business information before going live.
