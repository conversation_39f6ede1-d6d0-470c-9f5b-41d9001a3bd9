/**
 * EWIN ELECTRICAL - Simple Router for Partial Loading (Optional)
 * This file provides optional functionality for loading page partials
 */

const Router = {
    // Configuration
    config: {
        partialPath: '/partials/',
        contentSelector: '#main-content',
        loadingClass: 'loading'
    },

    // Initialize router
    init() {
        this.bindEvents();
        console.log('Router initialized (optional functionality)');
    },

    // Bind navigation events
    bindEvents() {
        document.addEventListener('click', this.handleNavClick.bind(this));
        window.addEventListener('popstate', this.handlePopState.bind(this));
    },

    // Handle navigation clicks
    handleNavClick(e) {
        const link = e.target.closest('a[data-partial]');
        if (!link) return;

        e.preventDefault();
        const partialName = link.getAttribute('data-partial');
        this.loadPartial(partialName);
    },

    // Handle browser back/forward
    handlePopState(e) {
        if (e.state && e.state.partial) {
            this.loadPartial(e.state.partial, false);
        }
    },

    // Load partial content
    async loadPartial(partialName, pushState = true) {
        const contentElement = document.querySelector(this.config.contentSelector);
        if (!contentElement) return;

        try {
            // Show loading state
            contentElement.classList.add(this.config.loadingClass);

            // Fetch partial
            const response = await fetch(`${this.config.partialPath}${partialName}.html`);
            if (!response.ok) throw new Error(`Failed to load ${partialName}`);

            const html = await response.text();
            
            // Update content
            contentElement.innerHTML = html;
            
            // Update browser history
            if (pushState) {
                history.pushState(
                    { partial: partialName }, 
                    '', 
                    `/${partialName}.html`
                );
            }

            // Update page title
            this.updatePageTitle(partialName);

            // Reinitialize components
            this.reinitializeComponents();

            // Analytics
            if (window.analytics) {
                window.analytics.logEvent('partial_loaded', { partial: partialName });
            }

        } catch (error) {
            console.error('Failed to load partial:', error);
            // Fallback to full page navigation
            window.location.href = `/${partialName}.html`;
        } finally {
            contentElement.classList.remove(this.config.loadingClass);
        }
    },

    // Update page title based on partial
    updatePageTitle(partialName) {
        const titles = {
            'services': 'Services - EWIN ELECTRICAL',
            'pricing': 'Pricing - EWIN ELECTRICAL',
            'about': 'About - EWIN ELECTRICAL',
            'gallery': 'Gallery - EWIN ELECTRICAL',
            'faqs': 'FAQs - EWIN ELECTRICAL',
            'contact': 'Contact - EWIN ELECTRICAL',
            'suburbs': 'Service Areas - EWIN ELECTRICAL'
        };

        document.title = titles[partialName] || 'EWIN ELECTRICAL';
    },

    // Reinitialize components after partial load
    reinitializeComponents() {
        // Reinitialize lazy loading
        if (window.LazyLoader) {
            window.LazyLoader.observeImages();
            window.LazyLoader.observeContent();
        }

        // Reinitialize form validation
        if (window.Validator) {
            // Validation is automatically bound to document events
        }

        // Reinitialize any other components as needed
        this.initializeNewContent();
    },

    // Initialize content-specific functionality
    initializeNewContent() {
        // Service filtering
        const filterButtons = document.querySelectorAll('.filter-btn');
        if (filterButtons.length > 0) {
            this.initializeServiceFiltering();
        }

        // Accordion functionality
        const accordions = document.querySelectorAll('.accordion');
        if (accordions.length > 0) {
            this.initializeAccordions();
        }

        // Gallery functionality
        const gallery = document.querySelector('.gallery-grid');
        if (gallery) {
            this.initializeGallery();
        }
    },

    // Initialize service filtering
    initializeServiceFiltering() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const serviceCards = document.querySelectorAll('.service-card');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Filter services
                serviceCards.forEach(card => {
                    const categories = card.getAttribute('data-categories');
                    
                    if (filter === 'all' || categories.includes(filter)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    },

    // Initialize accordion functionality
    initializeAccordions() {
        const accordionHeaders = document.querySelectorAll('.accordion__header');
        
        accordionHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const item = this.closest('.accordion__item');
                const content = item.querySelector('.accordion__content');
                const isActive = item.classList.contains('active');
                
                // Close all other accordion items
                const allItems = document.querySelectorAll('.accordion__item');
                allItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                        otherItem.querySelector('.accordion__header').setAttribute('aria-expanded', 'false');
                    }
                });
                
                // Toggle current item
                if (isActive) {
                    item.classList.remove('active');
                    this.setAttribute('aria-expanded', 'false');
                } else {
                    item.classList.add('active');
                    this.setAttribute('aria-expanded', 'true');
                }
            });
        });
    },

    // Initialize gallery functionality
    initializeGallery() {
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            item.addEventListener('click', function() {
                const img = this.querySelector('img');
                if (img) {
                    // Simple lightbox functionality
                    Router.openLightbox(img.src, img.alt);
                }
            });
        });
    },

    // Simple lightbox implementation
    openLightbox(src, alt) {
        const lightbox = document.createElement('div');
        lightbox.className = 'lightbox';
        lightbox.innerHTML = `
            <div class="lightbox__backdrop"></div>
            <div class="lightbox__content">
                <img src="${src}" alt="${alt}">
                <button class="lightbox__close" aria-label="Close lightbox">&times;</button>
            </div>
        `;
        
        // Add styles
        lightbox.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        const content = lightbox.querySelector('.lightbox__content');
        content.style.cssText = `
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        `;
        
        const img = lightbox.querySelector('img');
        img.style.cssText = `
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        `;
        
        const closeBtn = lightbox.querySelector('.lightbox__close');
        closeBtn.style.cssText = `
            position: absolute;
            top: -40px;
            right: 0;
            background: none;
            border: none;
            color: white;
            font-size: 30px;
            cursor: pointer;
            width: 40px;
            height: 40px;
        `;
        
        // Event listeners
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox || e.target.classList.contains('lightbox__backdrop')) {
                document.body.removeChild(lightbox);
            }
        });
        
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(lightbox);
        });
        
        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                document.body.removeChild(lightbox);
                document.removeEventListener('keydown', escHandler);
            }
        });
        
        document.body.appendChild(lightbox);
    }
};

// Export for use in other modules
window.Router = Router;

// Note: This router is optional and not required for the basic functionality
// The site works perfectly with standard HTML navigation
