/**
 * EWIN ELECTRICAL - Lazy Loading
 * Intersection Observer-based lazy loading for images and content
 */

const LazyLoader = {
    // Configuration
    config: {
        rootMargin: '50px 0px',
        threshold: 0.01,
        loadingClass: 'lazy-load',
        loadedClass: 'loaded',
        errorClass: 'error',
        retryAttempts: 3,
        retryDelay: 1000
    },

    // State
    state: {
        observer: null,
        loadedImages: new Set(),
        failedImages: new Map(),
        loadingImages: new Set()
    },

    // Initialize lazy loading
    init() {
        if (!window.IntersectionObserver) {
            console.warn('IntersectionObserver not supported, loading all images immediately');
            this.loadAllImages();
            return;
        }

        this.createObserver();
        this.observeImages();
        this.observeContent();
        
        console.log('Lazy loading initialized');
    },

    // Create intersection observer
    createObserver() {
        this.state.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                rootMargin: this.config.rootMargin,
                threshold: this.config.threshold
            }
        );
    },

    // Handle intersection events
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                if (element.tagName === 'IMG') {
                    this.loadImage(element);
                } else {
                    this.loadContent(element);
                }
                
                this.state.observer.unobserve(element);
            }
        });
    },

    // Observe images for lazy loading
    observeImages() {
        const images = document.querySelectorAll('img[data-src], img[data-srcset]');
        
        images.forEach(img => {
            // Add loading class
            img.classList.add(this.config.loadingClass);
            
            // Set placeholder if not already set
            if (!img.src && !img.getAttribute('src')) {
                img.src = this.generatePlaceholder(img);
            }
            
            // Observe the image
            this.state.observer.observe(img);
        });
    },

    // Observe content for lazy loading
    observeContent() {
        const elements = document.querySelectorAll('[data-lazy-content]');
        
        elements.forEach(element => {
            element.classList.add(this.config.loadingClass);
            this.state.observer.observe(element);
        });
    },

    // Load image
    loadImage(img) {
        if (this.state.loadingImages.has(img) || this.state.loadedImages.has(img)) {
            return;
        }

        this.state.loadingImages.add(img);

        const dataSrc = img.getAttribute('data-src');
        const dataSrcset = img.getAttribute('data-srcset');
        
        // Create a new image to preload
        const imageLoader = new Image();
        
        // Handle successful load
        imageLoader.onload = () => {
            this.handleImageLoad(img, dataSrc, dataSrcset);
        };
        
        // Handle load error
        imageLoader.onerror = () => {
            this.handleImageError(img, dataSrc);
        };
        
        // Start loading
        if (dataSrcset) {
            imageLoader.srcset = dataSrcset;
        }
        if (dataSrc) {
            imageLoader.src = dataSrc;
        }
    },

    // Handle successful image load
    handleImageLoad(img, dataSrc, dataSrcset) {
        // Set the actual src and srcset
        if (dataSrc) {
            img.src = dataSrc;
            img.removeAttribute('data-src');
        }
        
        if (dataSrcset) {
            img.srcset = dataSrcset;
            img.removeAttribute('data-srcset');
        }
        
        // Update classes
        img.classList.remove(this.config.loadingClass);
        img.classList.add(this.config.loadedClass);
        
        // Update state
        this.state.loadingImages.delete(img);
        this.state.loadedImages.add(img);
        
        // Trigger custom event
        img.dispatchEvent(new CustomEvent('lazyloaded', {
            detail: { src: dataSrc, srcset: dataSrcset }
        }));
        
        // Log analytics event
        if (window.analytics) {
            window.analytics.logEvent('image_loaded', {
                src: dataSrc,
                element: img.tagName.toLowerCase()
            });
        }
    },

    // Handle image load error
    handleImageError(img, dataSrc) {
        const attempts = this.state.failedImages.get(img) || 0;
        
        if (attempts < this.config.retryAttempts) {
            // Retry loading
            this.state.failedImages.set(img, attempts + 1);
            
            setTimeout(() => {
                this.loadImage(img);
            }, this.config.retryDelay * (attempts + 1));
            
        } else {
            // Give up and show error state
            img.classList.remove(this.config.loadingClass);
            img.classList.add(this.config.errorClass);
            
            // Set fallback image or alt text
            this.setImageFallback(img);
            
            this.state.loadingImages.delete(img);
            
            // Log error
            console.warn('Failed to load image after retries:', dataSrc);
            
            if (window.analytics) {
                window.analytics.logEvent('image_error', {
                    src: dataSrc,
                    attempts: attempts + 1
                });
            }
        }
    },

    // Set image fallback
    setImageFallback(img) {
        const fallbackSrc = img.getAttribute('data-fallback');
        
        if (fallbackSrc) {
            img.src = fallbackSrc;
        } else {
            // Create a simple placeholder
            img.src = this.generatePlaceholder(img, true);
        }
        
        // Ensure alt text is present
        if (!img.alt) {
            img.alt = 'Image could not be loaded';
        }
    },

    // Load content
    loadContent(element) {
        const contentType = element.getAttribute('data-lazy-content');
        
        switch (contentType) {
            case 'iframe':
                this.loadIframe(element);
                break;
            case 'video':
                this.loadVideo(element);
                break;
            case 'map':
                this.loadMap(element);
                break;
            default:
                this.loadGenericContent(element);
        }
    },

    // Load iframe content
    loadIframe(element) {
        const dataSrc = element.getAttribute('data-src');
        if (!dataSrc) return;
        
        const iframe = document.createElement('iframe');
        iframe.src = dataSrc;
        
        // Copy attributes
        ['width', 'height', 'frameborder', 'allowfullscreen', 'title'].forEach(attr => {
            const value = element.getAttribute(`data-${attr}`) || element.getAttribute(attr);
            if (value) {
                iframe.setAttribute(attr, value);
            }
        });
        
        // Replace element
        element.parentNode.replaceChild(iframe, element);
        
        if (window.analytics) {
            window.analytics.logEvent('iframe_loaded', { src: dataSrc });
        }
    },

    // Load video content
    loadVideo(element) {
        const dataSrc = element.getAttribute('data-src');
        if (!dataSrc) return;
        
        const video = document.createElement('video');
        video.src = dataSrc;
        
        // Copy attributes
        ['controls', 'autoplay', 'muted', 'loop', 'poster'].forEach(attr => {
            const value = element.getAttribute(`data-${attr}`) || element.getAttribute(attr);
            if (value !== null) {
                if (attr === 'poster') {
                    video.setAttribute(attr, value);
                } else {
                    video.setAttribute(attr, '');
                }
            }
        });
        
        element.parentNode.replaceChild(video, element);
        
        if (window.analytics) {
            window.analytics.logEvent('video_loaded', { src: dataSrc });
        }
    },

    // Load map content (e.g., Google Maps embed)
    loadMap(element) {
        const dataSrc = element.getAttribute('data-src');
        if (!dataSrc) return;
        
        const iframe = document.createElement('iframe');
        iframe.src = dataSrc;
        iframe.width = element.getAttribute('data-width') || '100%';
        iframe.height = element.getAttribute('data-height') || '300';
        iframe.style.border = '0';
        iframe.allowFullscreen = true;
        iframe.loading = 'lazy';
        iframe.title = element.getAttribute('data-title') || 'Map';
        
        element.parentNode.replaceChild(iframe, element);
        
        if (window.analytics) {
            window.analytics.logEvent('map_loaded', { src: dataSrc });
        }
    },

    // Load generic content
    loadGenericContent(element) {
        element.classList.remove(this.config.loadingClass);
        element.classList.add(this.config.loadedClass);
        
        // Trigger custom event
        element.dispatchEvent(new CustomEvent('lazyloaded'));
    },

    // Generate placeholder image
    generatePlaceholder(img, isError = false) {
        const width = img.getAttribute('width') || img.offsetWidth || 300;
        const height = img.getAttribute('height') || img.offsetHeight || 200;
        
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        if (isError) {
            // Error placeholder
            ctx.fillStyle = '#f3f4f6';
            ctx.fillRect(0, 0, width, height);
            
            ctx.fillStyle = '#9ca3af';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Image not available', width / 2, height / 2);
        } else {
            // Loading placeholder
            ctx.fillStyle = '#e5e7eb';
            ctx.fillRect(0, 0, width, height);
            
            // Add subtle pattern
            ctx.fillStyle = '#d1d5db';
            for (let i = 0; i < width; i += 20) {
                for (let j = 0; j < height; j += 20) {
                    if ((i + j) % 40 === 0) {
                        ctx.fillRect(i, j, 10, 10);
                    }
                }
            }
        }
        
        return canvas.toDataURL();
    },

    // Load all images immediately (fallback)
    loadAllImages() {
        const images = document.querySelectorAll('img[data-src], img[data-srcset]');
        
        images.forEach(img => {
            const dataSrc = img.getAttribute('data-src');
            const dataSrcset = img.getAttribute('data-srcset');
            
            if (dataSrc) {
                img.src = dataSrc;
                img.removeAttribute('data-src');
            }
            
            if (dataSrcset) {
                img.srcset = dataSrcset;
                img.removeAttribute('data-srcset');
            }
            
            img.classList.remove(this.config.loadingClass);
            img.classList.add(this.config.loadedClass);
        });
    },

    // Manually trigger loading for specific elements
    loadElement(element) {
        if (element.tagName === 'IMG') {
            this.loadImage(element);
        } else {
            this.loadContent(element);
        }
    },

    // Get loading statistics
    getStats() {
        return {
            loaded: this.state.loadedImages.size,
            loading: this.state.loadingImages.size,
            failed: this.state.failedImages.size,
            total: document.querySelectorAll('img[data-src], img[data-srcset], [data-lazy-content]').length
        };
    },

    // Destroy lazy loader
    destroy() {
        if (this.state.observer) {
            this.state.observer.disconnect();
            this.state.observer = null;
        }
        
        this.state.loadedImages.clear();
        this.state.failedImages.clear();
        this.state.loadingImages.clear();
    }
};

// Initialize lazy loading when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => LazyLoader.init());
} else {
    LazyLoader.init();
}

// Re-initialize on dynamic content changes
const lazyObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
                const lazyImages = node.querySelectorAll ? node.querySelectorAll('img[data-src], img[data-srcset]') : [];
                const lazyContent = node.querySelectorAll ? node.querySelectorAll('[data-lazy-content]') : [];
                
                [...lazyImages, ...lazyContent].forEach(element => {
                    if (LazyLoader.state.observer) {
                        LazyLoader.state.observer.observe(element);
                    }
                });
            }
        });
    });
});

// Observe document for changes
lazyObserver.observe(document.body, {
    childList: true,
    subtree: true
});

// Export for use in other modules
window.LazyLoader = LazyLoader;
