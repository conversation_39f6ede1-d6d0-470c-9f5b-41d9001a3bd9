/**
 * EWIN ELECTRICAL - Form Validation
 * Real-time form validation with accessible error messaging
 */

const Validator = {
    // Validation rules
    rules: {
        required: (value) => value.trim() !== '',
        email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        phone: (value) => /^(\+61|0)[2-9]\d{8}$/.test(value.replace(/\s/g, '')),
        minLength: (value, min) => value.length >= min,
        maxLength: (value, max) => value.length <= max
    },

    // Error messages
    messages: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        phone: 'Please enter a valid Australian phone number',
        minLength: 'Must be at least {min} characters',
        maxLength: 'Must be no more than {max} characters'
    },

    // Initialize validation
    init() {
        this.bindEvents();
        console.log('Form validation initialized');
    },

    // Bind validation events
    bindEvents() {
        document.addEventListener('input', this.handleInput.bind(this));
        document.addEventListener('blur', this.handleBlur.bind(this), true);
        document.addEventListener('submit', this.handleSubmit.bind(this));
    },

    // Handle input events for real-time validation
    handleInput(e) {
        const field = e.target;
        if (!this.isValidationField(field)) return;

        // Clear previous errors on input
        this.clearFieldError(field);
        
        // Validate on input for immediate feedback
        if (field.value.trim() !== '') {
            this.validateField(field);
        }
    },

    // Handle blur events for validation
    handleBlur(e) {
        const field = e.target;
        if (!this.isValidationField(field)) return;

        this.validateField(field);
    },

    // Handle form submission
    handleSubmit(e) {
        const form = e.target;
        if (!form.classList.contains('form')) return;

        const isValid = this.validateForm(form);
        
        if (!isValid) {
            e.preventDefault();
            
            // Focus first error field
            const firstError = form.querySelector('.form__group.error input, .form__group.error select, .form__group.error textarea');
            if (firstError) {
                firstError.focus();
            }
            
            // Announce errors to screen readers
            this.announceErrors(form);
        }
    },

    // Check if field should be validated
    isValidationField(field) {
        return field.matches('.form__input, .form__select, .form__textarea') && 
               field.closest('.form__group');
    },

    // Validate entire form
    validateForm(form) {
        const fields = form.querySelectorAll('.form__input, .form__select, .form__textarea');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    },

    // Validate individual field
    validateField(field) {
        const group = field.closest('.form__group');
        if (!group) return true;

        const rules = this.getFieldRules(field);
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Check each rule
        for (const rule of rules) {
            const result = this.checkRule(value, rule);
            if (!result.valid) {
                isValid = false;
                errorMessage = result.message;
                break;
            }
        }

        // Update UI
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    },

    // Get validation rules for field
    getFieldRules(field) {
        const rules = [];
        const type = field.type || field.tagName.toLowerCase();
        
        // Required rule
        if (field.hasAttribute('required')) {
            rules.push({ type: 'required' });
        }

        // Type-specific rules
        switch (type) {
            case 'email':
                rules.push({ type: 'email' });
                break;
            case 'tel':
                rules.push({ type: 'phone' });
                break;
        }

        // Length rules
        const minLength = field.getAttribute('minlength');
        if (minLength) {
            rules.push({ type: 'minLength', value: parseInt(minLength) });
        }

        const maxLength = field.getAttribute('maxlength');
        if (maxLength) {
            rules.push({ type: 'maxLength', value: parseInt(maxLength) });
        }

        // Custom validation rules from data attributes
        if (field.dataset.validate) {
            const customRules = field.dataset.validate.split('|');
            customRules.forEach(rule => {
                const [type, value] = rule.split(':');
                rules.push({ type, value: value ? parseInt(value) : undefined });
            });
        }

        return rules;
    },

    // Check individual rule
    checkRule(value, rule) {
        const { type, value: ruleValue } = rule;
        
        // Skip validation for empty optional fields
        if (value === '' && type !== 'required') {
            return { valid: true };
        }

        const validator = this.rules[type];
        if (!validator) {
            console.warn(`Unknown validation rule: ${type}`);
            return { valid: true };
        }

        const isValid = ruleValue !== undefined ? 
            validator(value, ruleValue) : 
            validator(value);

        return {
            valid: isValid,
            message: isValid ? '' : this.getErrorMessage(type, ruleValue)
        };
    },

    // Get error message for rule
    getErrorMessage(type, value) {
        let message = this.messages[type] || 'Invalid value';
        
        if (value !== undefined) {
            message = message.replace(`{${type.replace('Length', '')}}`, value);
        }
        
        return message;
    },

    // Show field error
    showFieldError(field, message) {
        const group = field.closest('.form__group');
        if (!group) return;

        group.classList.add('error');
        field.setAttribute('aria-invalid', 'true');

        let errorElement = group.querySelector('.form__error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'form__error';
            errorElement.setAttribute('role', 'alert');
            errorElement.setAttribute('aria-live', 'polite');
            group.appendChild(errorElement);
        }

        errorElement.textContent = message;
        errorElement.classList.add('visible');
        
        // Associate error with field for screen readers
        const errorId = `error-${field.id || Math.random().toString(36).substr(2, 9)}`;
        errorElement.id = errorId;
        field.setAttribute('aria-describedby', errorId);
    },

    // Clear field error
    clearFieldError(field) {
        const group = field.closest('.form__group');
        if (!group) return;

        group.classList.remove('error');
        field.setAttribute('aria-invalid', 'false');
        field.removeAttribute('aria-describedby');

        const errorElement = group.querySelector('.form__error');
        if (errorElement) {
            errorElement.classList.remove('visible');
            errorElement.textContent = '';
        }
    },

    // Announce errors to screen readers
    announceErrors(form) {
        const errors = form.querySelectorAll('.form__group.error');
        if (errors.length === 0) return;

        const errorCount = errors.length;
        const message = `Form has ${errorCount} error${errorCount > 1 ? 's' : ''}. Please review and correct.`;
        
        // Create or update announcement element
        let announcer = document.getElementById('form-announcer');
        if (!announcer) {
            announcer = document.createElement('div');
            announcer.id = 'form-announcer';
            announcer.setAttribute('aria-live', 'assertive');
            announcer.setAttribute('aria-atomic', 'true');
            announcer.className = 'sr-only';
            document.body.appendChild(announcer);
        }

        announcer.textContent = message;
    },

    // Phone number formatting
    formatPhone(input) {
        let value = input.value.replace(/\D/g, '');
        
        // Handle Australian numbers
        if (value.startsWith('61')) {
            value = '+' + value;
        } else if (value.startsWith('0')) {
            // Format as 0X XXXX XXXX
            if (value.length > 2) {
                value = value.substring(0, 2) + ' ' + value.substring(2);
            }
            if (value.length > 7) {
                value = value.substring(0, 7) + ' ' + value.substring(7, 11);
            }
        }
        
        input.value = value;
    },

    // Email validation with common domain suggestions
    validateEmailWithSuggestions(input) {
        const value = input.value.trim();
        if (!value) return;

        const commonDomains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'bigpond.com', 'optusnet.com.au', 'telstra.com'
        ];

        const emailParts = value.split('@');
        if (emailParts.length === 2) {
            const domain = emailParts[1].toLowerCase();
            
            // Check for common typos
            const suggestions = commonDomains.filter(d => {
                return this.levenshteinDistance(domain, d) === 1;
            });

            if (suggestions.length > 0) {
                this.showEmailSuggestion(input, emailParts[0] + '@' + suggestions[0]);
            }
        }
    },

    // Show email suggestion
    showEmailSuggestion(input, suggestion) {
        const group = input.closest('.form__group');
        if (!group) return;

        let suggestionElement = group.querySelector('.email-suggestion');
        if (!suggestionElement) {
            suggestionElement = document.createElement('div');
            suggestionElement.className = 'email-suggestion';
            suggestionElement.style.cssText = `
                font-size: 0.875rem;
                color: var(--electric-300);
                margin-top: 0.25rem;
                cursor: pointer;
            `;
            group.appendChild(suggestionElement);
        }

        suggestionElement.innerHTML = `Did you mean <strong>${suggestion}</strong>?`;
        suggestionElement.onclick = () => {
            input.value = suggestion;
            suggestionElement.remove();
            this.validateField(input);
        };
    },

    // Calculate Levenshtein distance for typo detection
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
};

// Enhanced phone input handling
document.addEventListener('input', (e) => {
    if (e.target.type === 'tel') {
        Validator.formatPhone(e.target);
    }
});

// Enhanced email input handling
document.addEventListener('blur', (e) => {
    if (e.target.type === 'email') {
        Validator.validateEmailWithSuggestions(e.target);
    }
}, true);

// Initialize validation when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => Validator.init());
} else {
    Validator.init();
}

// Export for use in other modules
window.Validator = Validator;
