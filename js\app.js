/**
 * EWIN ELECTRICAL - Main Application JavaScript
 * Handles navigation, modals, Calendly integration, and core functionality
 */

// Configuration
const CONFIG = {
    CALENDLY_URL: 'https://calendly.com/YOUR-HANDLE/30min', // Replace with actual Calendly URL
    ANIMATION_DURATION: 250,
    SCROLL_THRESHOLD: 100,
    TESTIMONIAL_AUTO_PLAY: true,
    TESTIMONIAL_INTERVAL: 5000
};

// DOM Elements Cache
const DOM = {
    header: null,
    navToggle: null,
    nav: null,
    navLinks: null,
    backToTop: null,
    fab: null,
    modals: null,
    testimonialCarousel: null,
    currentYear: null
};

// Application State
const STATE = {
    isNavOpen: false,
    currentTestimonial: 0,
    testimonialInterval: null,
    isScrolled: false
};

/**
 * Initialize the application
 */
function init() {
    cacheDOM();
    bindEvents();
    setupCalendly();
    setupTestimonials();
    setupScrollEffects();
    setupAccessibility();
    updateCurrentYear();
    
    // Initialize reveal animations
    if (window.IntersectionObserver) {
        setupRevealAnimations();
    }
    
    console.log('EWIN ELECTRICAL app initialized');
}

/**
 * Cache DOM elements for performance
 */
function cacheDOM() {
    DOM.header = document.querySelector('.header');
    DOM.navToggle = document.querySelector('.header__nav-toggle');
    DOM.nav = document.querySelector('.header__nav');
    DOM.navLinks = document.querySelectorAll('.header__nav-link');
    DOM.backToTop = document.querySelector('.back-to-top');
    DOM.fab = document.querySelector('.fab');
    DOM.modals = document.querySelectorAll('.modal');
    DOM.testimonialCarousel = document.querySelector('.testimonials__carousel');
    DOM.currentYear = document.getElementById('current-year');
}

/**
 * Bind event listeners
 */
function bindEvents() {
    // Navigation toggle
    if (DOM.navToggle) {
        DOM.navToggle.addEventListener('click', toggleNavigation);
    }
    
    // Navigation links
    DOM.navLinks.forEach(link => {
        link.addEventListener('click', handleNavClick);
    });
    
    // Back to top button
    if (DOM.backToTop) {
        DOM.backToTop.addEventListener('click', scrollToTop);
    }
    
    // Scroll events
    window.addEventListener('scroll', throttle(handleScroll, 16));
    
    // Resize events
    window.addEventListener('resize', throttle(handleResize, 250));
    
    // Calendly buttons
    document.addEventListener('click', handleCalendlyClick);
    
    // Modal events
    document.addEventListener('click', handleModalClick);
    
    // Keyboard events
    document.addEventListener('keydown', handleKeydown);
    
    // Form submissions
    document.addEventListener('submit', handleFormSubmit);
    
    // Smooth scroll for anchor links
    document.addEventListener('click', handleAnchorClick);
}

/**
 * Setup Calendly integration
 */
function setupCalendly() {
    // Ensure Calendly widget is loaded
    if (typeof Calendly !== 'undefined') {
        console.log('Calendly widget loaded');
    } else {
        console.warn('Calendly widget not loaded');
    }
}

/**
 * Handle Calendly button clicks
 */
function handleCalendlyClick(e) {
    const calendlyBtn = e.target.closest('[data-calendly-popup]');
    if (!calendlyBtn) return;
    
    e.preventDefault();
    
    if (typeof Calendly !== 'undefined') {
        // Get prefill data from forms if available
        const prefillData = getFormPrefillData();
        
        Calendly.initPopupWidget({
            url: CONFIG.CALENDLY_URL,
            prefill: prefillData
        });
        
        // Analytics
        logEvent('calendly_open', { source: 'popup' });
    } else {
        // Fallback to direct link
        window.open(CONFIG.CALENDLY_URL, '_blank');
    }
}

/**
 * Get form data for Calendly prefill
 */
function getFormPrefillData() {
    const form = document.querySelector('#quote-form');
    if (!form) return {};
    
    const formData = new FormData(form);
    return {
        name: formData.get('name') || '',
        email: formData.get('email') || '',
        phone: formData.get('phone') || ''
    };
}

/**
 * Toggle mobile navigation
 */
function toggleNavigation() {
    STATE.isNavOpen = !STATE.isNavOpen;
    
    DOM.navToggle.classList.toggle('active', STATE.isNavOpen);
    DOM.nav.classList.toggle('active', STATE.isNavOpen);
    DOM.navToggle.setAttribute('aria-expanded', STATE.isNavOpen);
    
    // Prevent body scroll when nav is open
    document.body.style.overflow = STATE.isNavOpen ? 'hidden' : '';
    
    logEvent('nav_toggle', { open: STATE.isNavOpen });
}

/**
 * Handle navigation link clicks
 */
function handleNavClick(e) {
    const link = e.target;
    const href = link.getAttribute('href');
    
    // Close mobile nav
    if (STATE.isNavOpen) {
        toggleNavigation();
    }
    
    // Update active state
    DOM.navLinks.forEach(l => l.classList.remove('header__nav-link--active'));
    link.classList.add('header__nav-link--active');
    
    logEvent('nav_click', { page: href });
}

/**
 * Handle scroll events
 */
function handleScroll() {
    const scrollY = window.scrollY;
    const wasScrolled = STATE.isScrolled;
    STATE.isScrolled = scrollY > CONFIG.SCROLL_THRESHOLD;
    
    // Header background
    if (STATE.isScrolled !== wasScrolled) {
        DOM.header?.classList.toggle('scrolled', STATE.isScrolled);
    }
    
    // Back to top button
    if (DOM.backToTop) {
        DOM.backToTop.classList.toggle('visible', scrollY > 500);
    }
}

/**
 * Scroll to top
 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    
    logEvent('scroll_to_top');
}

/**
 * Handle window resize
 */
function handleResize() {
    // Close mobile nav on desktop
    if (window.innerWidth > 768 && STATE.isNavOpen) {
        toggleNavigation();
    }
}

/**
 * Setup testimonials carousel
 */
function setupTestimonials() {
    const carousel = DOM.testimonialCarousel;
    if (!carousel) return;
    
    const track = carousel.querySelector('.testimonials__track');
    const testimonials = carousel.querySelectorAll('.testimonial-card');
    const prevBtn = carousel.querySelector('.testimonials__btn--prev');
    const nextBtn = carousel.querySelector('.testimonials__btn--next');
    const dots = carousel.querySelectorAll('.testimonials__dot');
    
    if (!track || testimonials.length === 0) return;
    
    // Navigation buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => changeTestimonial(-1));
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => changeTestimonial(1));
    }
    
    // Dots navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToTestimonial(index));
    });
    
    // Auto-play
    if (CONFIG.TESTIMONIAL_AUTO_PLAY) {
        startTestimonialAutoPlay();
        
        // Pause on hover
        carousel.addEventListener('mouseenter', stopTestimonialAutoPlay);
        carousel.addEventListener('mouseleave', startTestimonialAutoPlay);
    }
    
    // Initialize
    updateTestimonialDisplay();
}

/**
 * Change testimonial
 */
function changeTestimonial(direction) {
    const testimonials = document.querySelectorAll('.testimonial-card');
    if (testimonials.length === 0) return;
    
    STATE.currentTestimonial += direction;
    
    if (STATE.currentTestimonial >= testimonials.length) {
        STATE.currentTestimonial = 0;
    } else if (STATE.currentTestimonial < 0) {
        STATE.currentTestimonial = testimonials.length - 1;
    }
    
    updateTestimonialDisplay();
}

/**
 * Go to specific testimonial
 */
function goToTestimonial(index) {
    STATE.currentTestimonial = index;
    updateTestimonialDisplay();
}

/**
 * Update testimonial display
 */
function updateTestimonialDisplay() {
    const track = document.querySelector('.testimonials__track');
    const dots = document.querySelectorAll('.testimonials__dot');
    
    if (!track) return;
    
    const translateX = -STATE.currentTestimonial * 100;
    track.style.transform = `translateX(${translateX}%)`;
    
    // Update dots
    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === STATE.currentTestimonial);
    });
}

/**
 * Start testimonial auto-play
 */
function startTestimonialAutoPlay() {
    stopTestimonialAutoPlay();
    STATE.testimonialInterval = setInterval(() => {
        changeTestimonial(1);
    }, CONFIG.TESTIMONIAL_INTERVAL);
}

/**
 * Stop testimonial auto-play
 */
function stopTestimonialAutoPlay() {
    if (STATE.testimonialInterval) {
        clearInterval(STATE.testimonialInterval);
        STATE.testimonialInterval = null;
    }
}

/**
 * Setup reveal animations
 */
function setupRevealAnimations() {
    const revealElements = document.querySelectorAll('.reveal');
    
    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                revealObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    revealElements.forEach(el => {
        revealObserver.observe(el);
    });
}

/**
 * Handle modal interactions
 */
function handleModalClick(e) {
    // Open modal
    const modalTrigger = e.target.closest('[data-modal]');
    if (modalTrigger) {
        e.preventDefault();
        const modalId = modalTrigger.getAttribute('data-modal');
        openModal(modalId);
        return;
    }
    
    // Close modal
    if (e.target.classList.contains('modal') || e.target.classList.contains('modal__close')) {
        closeModal();
    }
}

/**
 * Open modal
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Focus management
    const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (focusableElements.length > 0) {
        focusableElements[0].focus();
    }
    
    logEvent('modal_open', { modal: modalId });
}

/**
 * Close modal
 */
function closeModal() {
    const activeModal = document.querySelector('.modal.active');
    if (!activeModal) return;
    
    activeModal.classList.remove('active');
    document.body.style.overflow = '';
    
    logEvent('modal_close');
}

/**
 * Handle keyboard events
 */
function handleKeydown(e) {
    // Escape key closes modals
    if (e.key === 'Escape') {
        closeModal();
        
        if (STATE.isNavOpen) {
            toggleNavigation();
        }
    }
}

/**
 * Handle form submissions
 */
function handleFormSubmit(e) {
    const form = e.target;
    if (!form.classList.contains('form')) return;
    
    e.preventDefault();
    
    // Validate form
    if (!validateForm(form)) {
        return;
    }
    
    // Submit form (placeholder - integrate with EmailJS/Formspree)
    submitForm(form);
}

/**
 * Submit form (placeholder implementation)
 */
function submitForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Show loading state
    form.classList.add('loading');
    
    // Simulate form submission
    setTimeout(() => {
        form.classList.remove('loading');
        
        // Redirect to thank you page with data
        const params = new URLSearchParams(data);
        window.location.href = `/thanks.html?${params.toString()}`;
        
        logEvent('form_submit', { form: form.id });
    }, 1000);
}

/**
 * Handle anchor link clicks for smooth scrolling
 */
function handleAnchorClick(e) {
    const link = e.target.closest('a[href^="#"]');
    if (!link) return;
    
    const href = link.getAttribute('href');
    if (href === '#') return;
    
    const target = document.querySelector(href);
    if (!target) return;
    
    e.preventDefault();
    
    target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
    
    logEvent('anchor_click', { target: href });
}

/**
 * Setup accessibility features
 */
function setupAccessibility() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'sr-only';
    skipLink.style.position = 'absolute';
    skipLink.style.top = '-40px';
    skipLink.style.left = '6px';
    skipLink.style.background = 'var(--electric-500)';
    skipLink.style.color = 'white';
    skipLink.style.padding = '8px';
    skipLink.style.textDecoration = 'none';
    skipLink.style.zIndex = '100000';
    
    skipLink.addEventListener('focus', () => {
        skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
        skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
}

/**
 * Update current year in footer
 */
function updateCurrentYear() {
    if (DOM.currentYear) {
        DOM.currentYear.textContent = new Date().getFullYear();
    }
}

/**
 * Throttle function for performance
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Log events (placeholder for analytics)
 */
function logEvent(eventName, data = {}) {
    if (typeof analytics !== 'undefined' && analytics.logEvent) {
        analytics.logEvent(eventName, data);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}
